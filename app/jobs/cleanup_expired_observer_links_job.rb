# frozen_string_literal: true

class CleanupExpiredObserverLinksJob < ApplicationJob
  queue_as :default

  def perform
    Rails.logger.info "Starting cleanup of expired observer links"
    
    expired_links = StudentObserverLink.needs_cleanup
    
    if expired_links.any?
      Rails.logger.info "Found #{expired_links.count} expired observer links to clean up"
      
      expired_links.find_each do |link|
        begin
          link.mark_expired!
          Rails.logger.info "Marked observer link #{link.id} as expired (observer: #{link.observer_user_id}, student: #{link.observed_student_id})"
        rescue => e
          Rails.logger.error "Failed to mark observer link #{link.id} as expired: #{e.message}"
        end
      end
    else
      Rails.logger.info "No expired observer links found"
    end
  end
end
