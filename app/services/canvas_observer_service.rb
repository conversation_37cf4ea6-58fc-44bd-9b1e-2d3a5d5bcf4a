# frozen_string_literal: true

class CanvasObserverService
  def initialize(organization)
    @organization = organization
    @canvas_api = CanvasSync::CanvasApi.new(
      base_url: @organization.settings[:canvas][:base_url],
      token: @organization.settings[:canvas][:api_token]
    )
  end

  def create_observer_link(observer_id:, student_id:)
    begin
      # Create observer/observee relationship at account level
      # This creates the link at the parent consortium instance
      response = @canvas_api.post(
        "accounts/self/user_observers",
        {
          user_observer: {
            user_id: observer_id,
            observer_id: observer_id,
            observee_id: student_id
          }
        }
      )

      if response.success?
        {
          success: true,
          canvas_observer_id: response.body['id']
        }
      else
        {
          success: false,
          error: "Canvas API error: #{response.status} - #{response.body}"
        }
      end
    rescue => e
      Rails.logger.error "Failed to create Canvas observer link: #{e.message}"
      {
        success: false,
        error: e.message
      }
    end
  end

  def remove_observer_link(observer_id:, student_id:)
    begin
      # Find the observer relationship to delete
      observer_links = @canvas_api.get("accounts/self/user_observers")
      
      if observer_links.success?
        # Find the specific link between observer and student
        link_to_remove = observer_links.body.find do |link|
          link['user_id'] == observer_id && link['observee_id'] == student_id
        end

        if link_to_remove
          # Delete the specific observer link
          response = @canvas_api.delete("accounts/self/user_observers/#{link_to_remove['id']}")
          
          if response.success?
            {
              success: true
            }
          else
            {
              success: false,
              error: "Failed to delete Canvas observer link: #{response.status} - #{response.body}"
            }
          end
        else
          # Link not found, but that's okay - it might have been removed already
          {
            success: true,
            warning: "Observer link not found in Canvas (may have been removed already)"
          }
        end
      else
        {
          success: false,
          error: "Failed to fetch Canvas observer links: #{observer_links.status} - #{observer_links.body}"
        }
      end
    rescue => e
      Rails.logger.error "Failed to remove Canvas observer link: #{e.message}"
      {
        success: false,
        error: e.message
      }
    end
  end

  def get_observer_links(observer_id:)
    begin
      response = @canvas_api.get("accounts/self/user_observers", { user_id: observer_id })
      
      if response.success?
        {
          success: true,
          links: response.body
        }
      else
        {
          success: false,
          error: "Failed to fetch observer links: #{response.status} - #{response.body}"
        }
      end
    rescue => e
      Rails.logger.error "Failed to fetch Canvas observer links: #{e.message}"
      {
        success: false,
        error: e.message
      }
    end
  end
end
