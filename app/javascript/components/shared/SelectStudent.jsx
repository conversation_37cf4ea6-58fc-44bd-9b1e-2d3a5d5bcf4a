import React, { useState, useRef } from 'react'
import { TextInput } from '@instructure/ui-text-input'
import { IconSearchLine, IconXLine } from '@instructure/ui-icons'
import { IconButton } from '@instructure/ui-buttons'
import { View } from '@instructure/ui-view'

const StudentSearchInput = ({ onSearchChange, searchValue = '' }) => {
  const [value, setValue] = useState(searchValue)
  const inputRef = useRef(null)

  const handleChange = (e, val) => {
    setValue(val)
    if (onSearchChange) {
      onSearchChange(val)
    }
  }

  const handleClear = (e) => {
    e.stopPropagation()
    setValue('')
    if (onSearchChange) {
      onSearchChange('')
    }
    inputRef.current?.focus()
  }

  const renderClearButton = () => {
    if (!value.length) return null
    return (
      <IconButton
        type="button"
        size="small"
        withBackground={false}
        withBorder={false}
        screenReaderLabel="Clear search"
        onClick={handleClear}
      >
        <IconXLine />
      </IconButton>
    )
  }

  return (
    <View as="div">
      <TextInput
        placeholder="Search students by name or SIS ID"
        value={value}
        onChange={handleChange}
        inputRef={(el) => (inputRef.current = el)}
        renderBeforeInput={<IconSearchLine inline={false} />}
        renderAfterInput={renderClearButton()}
      />
    </View>
  )
}

export default StudentSearchInput
