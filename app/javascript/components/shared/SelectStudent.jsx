import React, { useState, useCallback, useRef } from 'react'
import { TextInput } from '@instructure/ui-text-input'
import { IconSearchLine, IconXLine } from '@instructure/ui-icons'
import { IconButton } from '@instructure/ui-buttons'
import { View } from '@instructure/ui-view'
import { Text } from '@instructure/ui-text'
import debounce from '@instructure/debounce'

import { searchStudents } from './../../utils/api'

const StudentSearchInput = ({ onStudentClick }) => {
  const [value, setValue] = useState('')
  const [loading, setLoading] = useState(false)
  const [results, setResults] = useState([])
  const inputRef = useRef(null)

  const fetchStudents = useCallback(
    debounce(async (query) => {
      if (query.length < 3) {
        setResults([])
        return
      }

      setLoading(true)
      try {
        const response = await searchStudents({ search: query })
        const users = response.data.data.map((user) => ({
          id: user.canvas_id,
          label: user.sortable_name,
          sisId: user.sis_id,
        }))
        setResults(users)
      } catch (error) {
        console.error('Failed to fetch students:', error)
      } finally {
        setLoading(false)
      }
    }, 500),
    []
  )

  const handleChange = (e, val) => {
    setValue(val)
    if (val.trim() !== '') {
      fetchStudents(val)
    } else {
      setResults([])
    }
  }

  const handleClear = (e) => {
    e.stopPropagation()
    setValue('')
    setResults([])
    inputRef.current?.focus()
  }

  const renderClearButton = () => {
    if (!value.length) return null
    return (
      <IconButton
        type="button"
        size="small"
        withBackground={false}
        withBorder={false}
        screenReaderLabel="Clear search"
        onClick={handleClear}
      >
        <IconXLine />
      </IconButton>
    )
  }

  return (
    <View as="div">
      <TextInput
        placeholder="search"
        value={value}
        onChange={handleChange}
        inputRef={(el) => (inputRef.current = el)}
        renderBeforeInput={<IconSearchLine inline={false} />}
        renderAfterInput={renderClearButton()}
      />

      {loading && <Text>Loading students...</Text>}


      {!loading && value.length >= 3 && results.length === 0 && (
        <Text>No students found.</Text>
      )}
    </View>
  )
}

export default StudentSearchInput
