import React, { useState, useCallback, useRef } from 'react'
import { TextInput } from '@instructure/ui-text-input'
import { IconSearchLine, IconXLine } from '@instructure/ui-icons'
import { IconButton } from '@instructure/ui-buttons'
import { View } from '@instructure/ui-view'
import { Text } from '@instructure/ui-text'
import { Flex } from '@instructure/ui-flex'
import { List } from '@instructure/ui-list'
import debounce from '@instructure/debounce'

import { searchStudents } from './../../utils/api'

const StudentSearchInput = ({ onStudentClick }) => {
  const [value, setValue] = useState('')
  const [loading, setLoading] = useState(false)
  const [results, setResults] = useState([])
  const inputRef = useRef(null)

  const fetchStudents = useCallback(
    debounce(async (query) => {
      if (query.length < 3) {
        setResults([])
        return
      }

      setLoading(true)
      try {
        const response = await searchStudents({ search: query })
        const users = response.data.data.map((user) => ({
          id: user.canvas_id,
          label: user.sortable_name,
          sisId: user.sis_id,
        }))
        setResults(users)
      } catch (error) {
        console.error('Failed to fetch students:', error)
      } finally {
        setLoading(false)
      }
    }, 500),
    []
  )

  const handleChange = (e, val) => {
    setValue(val)
    if (val.trim() !== '') {
      fetchStudents(val)
    } else {
      setResults([])
    }
  }

  const handleStudentSelect = (student) => {
    setValue('')
    setResults([])
    if (onStudentClick) {
      onStudentClick(student)
    }
  }

  const handleClear = (e) => {
    e.stopPropagation()
    setValue('')
    setResults([])
    inputRef.current?.focus()
  }

  const renderClearButton = () => {
    if (!value.length) return null
    return (
      <IconButton
        type="button"
        size="small"
        withBackground={false}
        withBorder={false}
        screenReaderLabel="Clear search"
        onClick={handleClear}
      >
        <IconXLine />
      </IconButton>
    )
  }

  return (
    <View as="div" position="relative">
      <TextInput
        placeholder="Search students (min 3 characters)"
        value={value}
        onChange={handleChange}
        inputRef={(el) => (inputRef.current = el)}
        renderBeforeInput={<IconSearchLine inline={false} />}
        renderAfterInput={renderClearButton()}
      />

      {loading && (
        <View
          as="div"
          position="absolute"
          background="primary"
          padding="small"
          borderRadius="medium"
          shadow="above"
          width="100%"
          zIndex="topmost"
        >
          <Text>Loading students...</Text>
        </View>
      )}

      {!loading && value.length >= 3 && results.length === 0 && (
        <View
          as="div"
          position="absolute"
          background="primary"
          padding="small"
          borderRadius="medium"
          shadow="above"
          width="100%"
          zIndex="topmost"
        >
          <Text>No students found.</Text>
        </View>
      )}

      {!loading && results.length > 0 && (
        <View
          as="div"
          position="absolute"
          background="primary"
          borderRadius="medium"
          shadow="above"
          width="100%"
          zIndex="topmost"
          maxHeight="200px"
          overflowY="auto"
        >
          <List>
            {results.map((student) => (
              <List.Item
                key={student.id}
                onClick={() => handleStudentSelect(student)}
                style={{ cursor: 'pointer' }}
              >
                <Flex direction="column" padding="small">
                  <Text weight="bold">{student.label}</Text>
                  <Text size="small">SIS ID: {student.sisId || 'N/A'}</Text>
                </Flex>
              </List.Item>
            ))}
          </List>
        </View>
      )}
    </View>
  )
}

export default StudentSearchInput
