import React, { useState, useEffect } from 'react'
import { View, Flex, Text, Link, Button, Alert } from '@instructure/ui'
import StudentsTable from './StudentsTable'
import SelectStudent from './SelectStudent'
import CreateObserverLinkModal from './CreateObserverLinkModal'
import * as API from './../../utils/api'

const STUDENTS_PER_PAGE = 25

const StudentsList = () => {
  const [loading, setLoading] = useState(true)
  const [sortBy, setSortBy] = useState('users.sortable_name')
  const [ascending, setAscending] = useState(true)
  const [currentPage, setCurrentPage] = useState(1)

  const [totalStudents, setTotalStudents] = useState(0)
  const [totalPages, setTotalPages] = useState(1)

  const [students, setStudents] = useState([])

  // Observer link creation state
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [selectedStudent, setSelectedStudent] = useState(null)
  const [actionLoading, setActionLoading] = useState(false)
  const [error, setError] = useState(null)

  const handleStudentClick = (id) => {
    // This function can be used for future functionality
    console.log('Student clicked:', id)
  }

  // Fetch students
  useEffect(() => {
    const fetchStudents = async () => {
      setLoading(true)
      try {
        const response = await API.getStudents({
          per_page: STUDENTS_PER_PAGE,
          page: currentPage,
          sort_by: sortBy,
          sort_order: ascending ? 'ASC' : 'DESC',
        })
        const data = response.data
        setStudents(data.users)
        setTotalStudents(data.pagination.total_items)
        setTotalPages(data.pagination.total_pages)
      } catch (error) {
        console.error('Error fetching students:', error)
        setError('Failed to fetch students')
      } finally {
        setLoading(false)
      }
    }

    fetchStudents()
  }, [currentPage, sortBy, ascending])

  const handleSort = (column) => {
    if (sortBy === column) {
      setAscending(!ascending)
    } else {
      setSortBy(column)
      setAscending(true)
    }
  }

  const handleCreateObserverLink = (student) => {
    setSelectedStudent(student)
    setShowCreateModal(true)
  }

  const handleConfirmCreateLink = async (student) => {
    setActionLoading(true)
    setError(null)
    try {
      await API.createObserverLink(student.canvas_id)
      setShowCreateModal(false)
      setSelectedStudent(null)
      // Refresh the page to show the new state (calendar component)
      window.location.reload()
    } catch (error) {
      console.error('Error creating observer link:', error)
      setError(error.response?.data?.error || 'Failed to create observer link')
    } finally {
      setActionLoading(false)
    }
  }

  return (
    <View>
      <Flex justifyItems="space-between" margin="0 0 large 0">
        <Flex.Item shouldGrow>
          <Text size="large" weight="bold">Student Observer Tool</Text>
        </Flex.Item>
      </Flex>

      {error && (
        <Alert variant="error" margin="0 0 medium 0">
          {error}
        </Alert>
      )}

      <Flex
        justifyItems="space-between"
        alignItems="center"
        padding="none 0 medium 0"
      >
        <Flex.Item shouldGrow>
          <Text size="large" weight="bold">
            Students
          </Text>
        </Flex.Item>

        <Flex.Item>
          <SelectStudent onStudentClick={handleStudentClick} />
        </Flex.Item>
      </Flex>

      <StudentsTable
        students={students}
        onStudentClick={handleStudentClick}
        onCreateObserverLink={handleCreateObserverLink}
        loading={loading}
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        totalPages={totalPages}
        sortBy={sortBy}
        ascending={ascending}
        handleSort={handleSort}
      />

      <CreateObserverLinkModal
        isOpen={showCreateModal}
        onClose={() => {
          setShowCreateModal(false)
          setSelectedStudent(null)
        }}
        student={selectedStudent}
        onConfirm={handleConfirmCreateLink}
        loading={actionLoading}
      />
    </View>
  )
}

export default StudentsList
