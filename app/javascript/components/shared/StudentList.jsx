import React, { useState, useEffect } from 'react'
import { View, Flex, Text, Link, Button } from '@instructure/ui'
import StudentsTable from './StudentsTable'
import SelectStudent from './SelectStudent'
import CreateObserverLinkModal from './CreateObserverLinkModal'
import ObserverLinkStatus from './ObserverLinkStatus'
import * as API from './../../utils/api'

const STUDENTS_PER_PAGE = 25

const StudentsList = () => {
  const [loading, setLoading] = useState(true)
  const [sortBy, setSortBy] = useState('users.sortable_name')
  const [ascending, setAscending] = useState(true)
  const [currentPage, setCurrentPage] = useState(1)

  const [totalStudents, setTotalStudents] = useState(0)
  const [totalPages, setTotalPages] = useState(1)

  const [students, setStudents] = useState([])

  const handleStudentClick = (id) => {
    setSelectedStudentId(id)
  }

  useEffect(() => {
    const fetchStudents = async () => {
      await API.getStudents({
        per_page: STUDENTS_PER_PAGE,
        page: currentPage,
        sort_by: sortBy,
        sort_order: ascending ? 'ASC' : 'DESC',
      })
        .then((response) => response.data)
        .then((data) => {
          setStudents(data.users)
          setTotalStudents(data.pagination.total_items)
          setTotalPages(data.pagination.total_pages)
          setLoading(false)
        })
        .catch((error) => {
          setLoading(false)
          console.error('Error fetching students:', error)
        })
    }

    fetchStudents()
  }, [currentPage, sortBy, ascending])

  const handleSort = (column) => {
    if (sortBy === column) {
      setAscending(!ascending)
    } else {
      setSortBy(column)
      setAscending(true)
    }
  }

  return (
    <View>
        <>
          <Flex justifyItems="space-between" margin="0 0 large 0">
            <Flex.Item shouldGrow>
              <Text size="large" weight="bold">Student Observer Tool</Text>
            </Flex.Item>
          </Flex>


          <Flex
            justifyItems="space-between"
            alignItems="center"
            padding="none 0 medium 0"
          >
            <Flex.Item shouldGrow>
              <Text size="large" weight="bold">
                Students
              </Text>
            </Flex.Item>

            <Flex.Item>
              <SelectStudent onStudentClick={handleStudentClick} />
            </Flex.Item>
          </Flex>

          <StudentsTable
            students={students}
            onStudentClick={handleStudentClick}
            loading={loading}
            currentPage={currentPage}
            setCurrentPage={setCurrentPage}
            totalPages={totalPages}
            sortBy={sortBy}
            ascending={ascending}
            handleSort={handleSort}
          />

        </>
    </View>
  )
}

export default StudentsList
