import React, { useState, useEffect } from 'react'
import { Table, Text, Flex, Link, Spinner, Menu } from '@instructure/ui'
import {
  IconMoreSolid,
  IconMiniArrowUpLine,
  IconMiniArrowDownLine,
} from '@instructure/ui-icons'
import { Pagination } from '@instructure/ui-pagination'


const StudentsTable = ({
  onStudentClick,
  students,
  loading,
  currentPage,
  setCurrentPage,
  totalPages,
  sortBy,
  ascending,
  handleSort,
  onCreateObserverLink,
}) => {

  const renderSortIcon = (column) => {
    if (sortBy === column) {
      return ascending ? <IconMiniArrowUpLine /> : <IconMiniArrowDownLine />
    }
    return null
  }



  const renderTableHeaders = () => (
    <Table.Row>
      <Table.ColHeader
        id="student-name"
        onClick={() => handleSort('users.sortable_name')}
        as="button"
        textAlign="start" margin="medium"
      >
        Student Name A-Z {renderSortIcon('users.sortable_name')}
      </Table.ColHeader>
      <Table.ColHeader id="contact-info" >
         SIS ID
      </Table.ColHeader>
      <Table.ColHeader id="contact-info" textAlign="end">
        Actions
      </Table.ColHeader>
    </Table.Row>
  )

  const renderTableBody = () =>
    students.map((student) => (
    <Table.Row key={student.canvas_id}>
  <Table.Cell>
    <Flex direction="row" align="center" padding="xx-small 0">
      <Flex.Item margin="0 small 0 0">
        <Text weight="bold" size="medium">
          {student.sortable_name}
        </Text>
      </Flex.Item>
    </Flex>
  </Table.Cell>

  <Table.Cell>
    <Text size="small" margin="0 small">{student.sis_id}</Text>
  </Table.Cell>

  <Table.Cell textAlign="end">
    <Menu
      trigger={<IconMoreSolid cursor="pointer" title="More actions" />}
      placement="bottom end"
    >
      <Menu.Item
        onSelect={() => onCreateObserverLink && onCreateObserverLink(student)}
      >
        Create temporary observer link
      </Menu.Item>
    </Menu>
  </Table.Cell>
</Table.Row>

    ))

  if (loading) {
    return (
      <Flex alignItems="center" justifyItems="center" padding="xx-large">
        <Spinner renderTitle="Loading students..." />
      </Flex>
    )
  }

  return (
    <>
      <Table caption={false} layout="auto" hover>
        <Table.Head>{renderTableHeaders()}</Table.Head>
        <Table.Body>{renderTableBody()}</Table.Body>
      </Table>

      {totalPages > 1 && (
        <Pagination
          as="nav"
          margin="large small small"
          variant="compact"
          labelNext="Next Page"
          labelPrev="Previous Page"
          labelFirst="First Page"
          labelLast="Last Page"
          withFirstAndLastButton
          currentPage={currentPage}
          onPageChange={(nextPage) => setCurrentPage(nextPage)}
          totalPageNumber={totalPages}
          siblingCount={3}
          boundaryCount={2}
        />
      )}


    </>
  )
}

export default StudentsTable
