import React, { useState } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Text,
  <PERSON>lex,
  Spinner,
  <PERSON>ert
} from '@instructure/ui'

const CreateObserverLinkModal = ({
  isOpen,
  onClose,
  student,
  onConfirm,
  loading = false
}) => {
  const [error, setError] = useState(null)

  const handleConfirm = async () => {
    try {
      setError(null)
      await onConfirm(student)
    } catch (err) {
      setError(err.message || 'Failed to create observer link')
    }
  }

  const handleClose = () => {
    setError(null)
    onClose()
  }

  if (!student) return null

  return (
    <Modal
      open={isOpen}
      onDismiss={handleClose}
      size="medium"
      label="Create Temporary Observer Link"
      shouldCloseOnDocumentClick={false}
    >
      <Modal.Header>
        <Text size="large" weight="bold">
          Create a Temporary Observer Link
        </Text>
      </Modal.Header>

      <Modal.Body>
        <Flex direction="column" gap="medium">
          {error && (
            <Alert variant="error" margin="0 0 medium 0">
              {error}
            </Alert>
          )}

          <Text>
            Creating a Temporary Observer Link allows you temporary viewing of
            this student's courses for 1 hour. Link will expire after 1 hour and can be renewed only once.
          </Text>

          <Flex direction="column" gap="small" margin="medium 0">
            <Text weight="bold">Student Name:</Text>
            <Text>{student.sortable_name}</Text>
          </Flex>

          <Flex direction="column" gap="small">
            <Text weight="bold">SIS ID:</Text>
            <Text>{student.sis_id || 'N/A'}</Text>
          </Flex>

          <Alert variant="info" margin="medium 0 0 0">
            <Text size="small">
              <strong>Note:</strong> You can only have one active observer link at a time.
              If you currently have an active link, you must end it before creating a new one.
            </Text>
          </Alert>
        </Flex>
      </Modal.Body>

      <Modal.Footer>
        <Button
          onClick={handleClose}
          margin="0 x-small 0 0"
          disabled={loading}
        >
          Cancel
        </Button>
        <Button
          color="primary"
          onClick={handleConfirm}
          disabled={loading}
        >
          {loading ? (
            <Flex alignItems="center" gap="x-small">
              <Spinner size="x-small" renderTitle="Creating link..." />
              <Text>Creating...</Text>
            </Flex>
          ) : (
            'Create Observer Link'
          )}
        </Button>
      </Modal.Footer>
    </Modal>
  )
}

export default CreateObserverLinkModal
