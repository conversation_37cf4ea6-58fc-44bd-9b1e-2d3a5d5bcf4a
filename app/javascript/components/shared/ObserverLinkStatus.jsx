import React, { useState, useEffect } from 'react'
import {
  <PERSON>,
  <PERSON>lex,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Spin<PERSON>
} from '@instructure/ui'
import { IconClockLine, IconUserLine } from '@instructure/ui-icons'

const ObserverLinkStatus = ({ 
  observerLink, 
  onEndLink, 
  onRenewLink,
  loading = false 
}) => {
  const [timeRemaining, setTimeRemaining] = useState(observerLink?.time_remaining_minutes || 0)

  useEffect(() => {
    if (!observerLink) return

    const timer = setInterval(() => {
      const now = new Date()
      const expiresAt = new Date(observerLink.expires_at)
      const remaining = Math.max(0, Math.floor((expiresAt - now) / (1000 * 60)))
      setTimeRemaining(remaining)
    }, 60000) // Update every minute

    return () => clearInterval(timer)
  }, [observerLink])

  const formatTimeRemaining = (minutes) => {
    if (minutes <= 0) return 'Expired'
    if (minutes < 60) return `${minutes} minute${minutes !== 1 ? 's' : ''}`
    
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    
    if (remainingMinutes === 0) {
      return `${hours} hour${hours !== 1 ? 's' : ''}`
    }
    
    return `${hours}h ${remainingMinutes}m`
  }

  const getStatusVariant = () => {
    if (timeRemaining <= 0) return 'error'
    if (timeRemaining <= 10) return 'warning'
    return 'success'
  }

  if (!observerLink) return null

  return (
    <View>
      <Flex justifyItems="space-between" margin="0 0 large 0">
        <Flex.Item shouldGrow>
          <Text size="large" weight="bold">Student Observer Tool</Text>
        </Flex.Item>
      </Flex>

      <Alert variant={getStatusVariant()} margin="0 0 large 0">
        <Flex direction="column" gap="small">
          <Text weight="bold">
            {timeRemaining <= 0 ? 'Observer Link Expired' : 'Active Observer Link'}
          </Text>
          <Flex alignItems="center" gap="small">
            <IconClockLine />
            <Text>
              Time remaining: {formatTimeRemaining(timeRemaining)}
            </Text>
          </Flex>
        </Flex>
      </Alert>

      <View 
        as="div" 
        background="primary" 
        padding="medium" 
        borderRadius="medium"
        margin="0 0 large 0"
      >
        <Flex direction="column" gap="medium">
          <Text size="large" weight="bold" color="primary-inverse">
            Currently Observing
          </Text>
          
          <Flex alignItems="center" gap="small">
            <IconUserLine color="primary-inverse" />
            <Flex direction="column" gap="x-small">
              <Text color="primary-inverse" weight="bold">
                {observerLink.observed_student.sortable_name}
              </Text>
              <Text color="primary-inverse" size="small">
                SIS ID: {observerLink.observed_student.sis_id || 'N/A'}
              </Text>
            </Flex>
          </Flex>

          <Flex direction="column" gap="x-small">
            <Text color="primary-inverse" size="small">
              <strong>Created:</strong> {new Date(observerLink.created_at).toLocaleString()}
            </Text>
            <Text color="primary-inverse" size="small">
              <strong>Expires:</strong> {new Date(observerLink.expires_at).toLocaleString()}
            </Text>
            {observerLink.renewed_at && (
              <Text color="primary-inverse" size="small">
                <strong>Renewed:</strong> {new Date(observerLink.renewed_at).toLocaleString()}
              </Text>
            )}
          </Flex>
        </Flex>
      </View>

      <Flex gap="medium" justifyItems="start">
        {observerLink.can_be_renewed && timeRemaining > 0 && (
          <Button
            color="secondary"
            onClick={onRenewLink}
            disabled={loading}
          >
            {loading ? (
              <Flex alignItems="center" gap="x-small">
                <Spinner size="x-small" renderTitle="Renewing..." />
                <Text>Renewing...</Text>
              </Flex>
            ) : (
              'Renew Link (1 hour)'
            )}
          </Button>
        )}
        
        <Button
          color="danger"
          onClick={onEndLink}
          disabled={loading}
        >
          {loading ? (
            <Flex alignItems="center" gap="x-small">
              <Spinner size="x-small" renderTitle="Ending link..." />
              <Text>Ending...</Text>
            </Flex>
          ) : (
            'End Observer Link'
          )}
        </Button>
      </Flex>

      {timeRemaining <= 0 && (
        <Alert variant="info" margin="large 0 0 0">
          <Text>
            This observer link has expired. Click "End Observer Link" to return to the student list.
          </Text>
        </Alert>
      )}
    </View>
  )
}

export default ObserverLinkStatus
