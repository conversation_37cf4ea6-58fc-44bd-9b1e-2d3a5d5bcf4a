import axios from './axios'

export const getStudents = (params = {}) => {
  return axios.get('/students', params)
}

export const searchStudents = (params = {}) => {
  return axios.get(`students/search`, { params: params })
}

export const createObserverLink = (studentId) => {
  return axios.post(`students/${studentId}/create_observer_link`)
}

export const getCurrentObserverLink = () => {
  return axios.get('observer_link/current')
}

export const renewObserverLink = () => {
  return axios.patch('observer_link/renew')
}

export const endObserverLink = () => {
  return axios.delete('observer_link/end')
}
