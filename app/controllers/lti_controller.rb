# frozen_string_literal: true

class LtiController < ApplicationController
  before_action :set_js_env

  def account_navigation
    authorize! :launch_from, :account
    component = if current_ability.user_is_account_admin?
                  determine_component_for_user
                else
                  'UnAuthorized'
                end
    render component:, prerender: false
  end

  def course_navigation
    authorize! :launch_from, :course
    component = if current_ability.user_is_course_admin?
                  determine_component_for_user
                else
                  'UnAuthorized'
                end
    render component:, prerender: false
  end

  private

  def determine_component_for_user
    # Check if user has an active observer link
    active_link = StudentObserverLink.active.for_observer(current_canvas_user_id).first

    if active_link
      # For now, return the same dashboard - we'll add calendar component later
      # TODO: Return 'CalendarDashboard' when calendar functionality is implemented
      current_ability.launch_context.is_a?(Account) ? 'AdminDashboard' : 'CourseDashboard'
    else
      # Return appropriate dashboard for creating observer links
      current_ability.launch_context.is_a?(Account) ? 'AdminDashboard' : 'CourseDashboard'
    end
  end

  def set_js_env
    js_env({
             launch_point: action_name
           })
  end
end
