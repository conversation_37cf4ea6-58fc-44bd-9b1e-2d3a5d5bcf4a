# frozen_string_literal: true

class LtiController < ApplicationController
  before_action :set_js_env

  def account_navigation
    authorize! :launch_from, :account
    component = if current_ability.user_is_account_admin?
                  'AdminDashboard'
                else
                  'UnAuthorized'
                end
    render component:, prerender: false
  end

  def course_navigation
    authorize! :launch_from, :course
    component = if current_ability.user_is_course_admin?
                  'CourseDashboard'
                else
                  'UnAuthorized'
                end
    render component:, prerender: false
  end

  private

  def set_js_env
    js_env({
             launch_point: action_name
           })
  end
end
