# frozen_string_literal: true

class Api::V1::ObserverLinksController < ApplicationController
  before_action :set_observer_link, only: [:renew, :end_link]

  def current
    authorize! :manage, StudentObserverLink

    observer_link = StudentObserverLink.active.for_observer(current_canvas_user_id).first

    if observer_link
      render json: {
        data: format_observer_link(observer_link)
      }, status: :ok
    else
      render json: { data: nil }, status: :ok
    end
  end

  def renew
    authorize! :manage, StudentObserverLink

    if @observer_link.renew!
      render json: {
        data: format_observer_link(@observer_link),
        message: 'Observer link renewed successfully'
      }, status: :ok
    else
      render json: {
        error: 'This link cannot be renewed or has already been renewed.'
      }, status: :unprocessable_entity
    end
  end

  def end_link
    authorize! :manage, StudentObserverLink

    @observer_link.end_link!

    render json: {
      message: 'Observer link ended successfully'
    }, status: :ok
  end

  private

  def set_observer_link
    @observer_link = StudentObserverLink.active.for_observer(current_canvas_user_id).first
    unless @observer_link
      render json: { error: 'No active observer link found' }, status: :not_found
    end
  end

  def format_observer_link(observer_link)
    {
      id: observer_link.id,
      observer_user_id: observer_link.observer_user_id,
      observed_student: {
        canvas_id: observer_link.observed_student.canvas_id,
        sortable_name: observer_link.observed_student.sortable_name,
        sis_id: observer_link.observed_student.pseudonyms.first&.sis_id
      },
      created_at: observer_link.created_at,
      expires_at: observer_link.expires_at,
      renewed_at: observer_link.renewed_at,
      time_remaining_minutes: observer_link.time_remaining,
      can_be_renewed: observer_link.can_be_renewed?,
      status: observer_link.status
    }
  end
end
