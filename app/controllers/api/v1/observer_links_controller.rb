# frozen_string_literal: true

class Api::V1::ObserverLinksController < ApplicationController
  before_action :set_observer_link, only: [:show, :renew, :destroy]
  before_action :set_student, only: [:create]

  def index
    # Get current active link for the observer
    @observer_link = StudentObserverLink.active.for_observer(current_canvas_user_id).first
    
    if @observer_link
      render json: {
        data: {
          id: @observer_link.id,
          observer_user_id: @observer_link.observer_user_id,
          observed_student: {
            canvas_id: @observer_link.observed_student.canvas_id,
            sortable_name: @observer_link.observed_student.sortable_name,
            sis_id: @observer_link.observed_student.pseudonyms.first&.sis_id
          },
          expires_at: @observer_link.expires_at,
          renewed_at: @observer_link.renewed_at,
          time_remaining_minutes: @observer_link.time_remaining,
          can_be_renewed: @observer_link.can_be_renewed?,
          status: @observer_link.status
        }
      }, status: :ok
    else
      render json: { data: nil }, status: :ok
    end
  end

  def create
    # Check if observer already has an active link
    existing_link = StudentObserverLink.active.for_observer(current_canvas_user_id).first
    if existing_link
      return render json: {
        error: 'You already have an active observer link. Please end the current link before creating a new one.'
      }, status: :unprocessable_entity
    end

    # Create the observer link in our database
    @observer_link = StudentObserverLink.new(
      observer_user_id: current_canvas_user_id,
      observed_student_id: @student.canvas_id
    )

    if @observer_link.save
      # Create the actual Canvas observer link
      canvas_service = CanvasObserverService.new(current_organization)
      canvas_result = canvas_service.create_observer_link(
        observer_id: current_canvas_user_id,
        student_id: @student.canvas_id
      )

      if canvas_result[:success]
        render json: {
          data: {
            id: @observer_link.id,
            observer_user_id: @observer_link.observer_user_id,
            observed_student: {
              canvas_id: @observer_link.observed_student.canvas_id,
              sortable_name: @observer_link.observed_student.sortable_name,
              sis_id: @observer_link.observed_student.pseudonyms.first&.sis_id
            },
            expires_at: @observer_link.expires_at,
            time_remaining_minutes: @observer_link.time_remaining,
            can_be_renewed: @observer_link.can_be_renewed?,
            status: @observer_link.status
          }
        }, status: :created
      else
        @observer_link.destroy
        render json: {
          error: "Failed to create Canvas observer link: #{canvas_result[:error]}"
        }, status: :unprocessable_entity
      end
    else
      render json: {
        error: @observer_link.errors.full_messages.join(', ')
      }, status: :unprocessable_entity
    end
  end

  def renew
    if @observer_link.renew!
      render json: {
        data: {
          id: @observer_link.id,
          expires_at: @observer_link.expires_at,
          renewed_at: @observer_link.renewed_at,
          time_remaining_minutes: @observer_link.time_remaining,
          can_be_renewed: @observer_link.can_be_renewed?
        }
      }, status: :ok
    else
      render json: {
        error: 'This link cannot be renewed or has already been renewed.'
      }, status: :unprocessable_entity
    end
  end

  def destroy
    # Remove the Canvas observer link
    canvas_service = CanvasObserverService.new(current_organization)
    canvas_result = canvas_service.remove_observer_link(
      observer_id: @observer_link.observer_user_id,
      student_id: @observer_link.observed_student_id
    )

    # Mark our link as ended regardless of Canvas API result
    @observer_link.end_link!

    if canvas_result[:success]
      render json: { message: 'Observer link ended successfully' }, status: :ok
    else
      # Still return success since our link is ended, but log the Canvas error
      Rails.logger.warn "Failed to remove Canvas observer link: #{canvas_result[:error]}"
      render json: { 
        message: 'Observer link ended successfully',
        warning: 'There may have been an issue removing the Canvas link'
      }, status: :ok
    end
  end

  private

  def set_observer_link
    @observer_link = StudentObserverLink.active.for_observer(current_canvas_user_id).find(params[:id])
  rescue ActiveRecord::RecordNotFound
    render json: { error: 'Observer link not found' }, status: :not_found
  end

  def set_student
    @student = User.find_by(canvas_id: params[:student_id])
    unless @student
      render json: { error: 'Student not found' }, status: :not_found
    end
  end

  def current_organization
    @current_organization ||= PandaPal::Organization.find_by(id: current_session.organization_id)
  end
end
