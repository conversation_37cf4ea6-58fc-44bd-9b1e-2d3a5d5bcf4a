# frozen_string_literal: true

class Api::V1::StudentsController < ApplicationController
  include Pagination

  def index
    students = fetch_students
               .order("#{sort_key} #{sort_order}")
    @paginated_data = paginated(students, pagination_params)
  end

  def search
    users = fetch_students
    render json: {
      data: users.where(search_query, search: "%#{params[:search]}%")
                 .as_json(only: [:canvas_id, :sis_id, :sortable_name])
    }, status: :ok
  end

  private

  def fetch_students
    if current_ability.launch_context.is_a?(Account)
      User.active_students_for_account(current_ability.launch_context)
    elsif current_ability.launch_context.is_a?(Course)
      User.active_students_in_course_ids([current_ability.launch_context])
    else
      User.none
    end
  end

  def search_query
    'first_name ILIKE :search OR last_name ILIKE :search OR sortable_name ILIKE :search'
  end

  def sort_key
    return params[:sort_key] if %w[users.sortable_name].include?(params[:sort_key])

    'users.sortable_name'
  end

  def sort_order
    params[:sort_order].to_s.upcase.in?(%w[ASC DESC]) ? params[:sort_order] : 'ASC'
  end
end
