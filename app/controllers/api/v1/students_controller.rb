# frozen_string_literal: true

class Api::V1::StudentsController < ApplicationController
  include Pagination

  def index
    students = fetch_students
               .order("#{sort_key} #{sort_order}")
    @paginated_data = paginated(students, pagination_params)
  end

  def search
    users = fetch_students
    render json: {
      data: users.where(search_query, search: "%#{params[:search]}%")
                 .as_json(only: [:canvas_id, :sis_id, :sortable_name])
    }, status: :ok
  end

  def create_observer_link
    authorize! :create_observer_link, User

    student = User.find_by(canvas_id: params[:id])
    unless student
      return render json: { error: 'Student not found' }, status: :not_found
    end

    # Check if observer already has an active link
    existing_link = StudentObserverLink.active.for_observer(current_canvas_user_id).first
    if existing_link
      return render json: {
        error: 'You already have an active observer link. Please end the current link before creating a new one.'
      }, status: :unprocessable_entity
    end

    # Create the observer link
    observer_link = StudentObserverLink.new(
      observer_user_id: current_canvas_user_id,
      observed_student_id: student.canvas_id
    )

    if observer_link.save
      render json: {
        data: format_observer_link(observer_link),
        message: 'Observer link created successfully'
      }, status: :created
    else
      render json: {
        error: observer_link.errors.full_messages.join(', ')
      }, status: :unprocessable_entity
    end
  end

  private

  def fetch_students
    if current_ability.launch_context.is_a?(Account)
      User.active_students_for_account(current_ability.launch_context)
    elsif current_ability.launch_context.is_a?(Course)
      User.active_students_in_course_ids([current_ability.launch_context])
    else
      User.none
    end
  end

  def format_observer_link(observer_link)
    {
      id: observer_link.id,
      observer_user_id: observer_link.observer_user_id,
      observed_student: {
        canvas_id: observer_link.observed_student.canvas_id,
        sortable_name: observer_link.observed_student.sortable_name,
        sis_id: observer_link.observed_student.pseudonyms.first&.sis_id
      },
      created_at: observer_link.created_at,
      expires_at: observer_link.expires_at,
      renewed_at: observer_link.renewed_at,
      time_remaining_minutes: observer_link.time_remaining,
      can_be_renewed: observer_link.can_be_renewed?,
      status: observer_link.status
    }
  end

  def search_query
    'first_name ILIKE :search OR last_name ILIKE :search OR sortable_name ILIKE :search'
  end

  def sort_key
    return params[:sort_key] if %w[users.sortable_name].include?(params[:sort_key])

    'users.sortable_name'
  end

  def sort_order
    params[:sort_order].to_s.upcase.in?(%w[ASC DESC]) ? params[:sort_order] : 'ASC'
  end
end
